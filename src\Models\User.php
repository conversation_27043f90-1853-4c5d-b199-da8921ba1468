<?php

namespace Fingerplus\Models;

class User
{
    /**
     * @var bool
     */
    public $result;

    /**
     * @var UserData[]
     */
    public $data;

    public function __construct(array $data = [])
    {
        $this->result = $data['Result'] ?? false;
        $this->data = [];
        
        if (isset($data['Data']) && is_array($data['Data'])) {
            foreach ($data['Data'] as $item) {
                $this->data[] = new UserData($item);
            }
        }
    }

    /**
     * Create from array data
     */
    public static function fromArray(array $data): self
    {
        return new self($data);
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        $dataArray = [];
        foreach ($this->data as $item) {
            $dataArray[] = $item->toArray();
        }

        return [
            'Result' => $this->result,
            'Data' => $dataArray
        ];
    }
}

class UserData
{
    /**
     * @var string
     */
    public $pin;

    /**
     * @var string
     */
    public $name;

    /**
     * @var string
     */
    public $rfid;

    /**
     * @var string
     */
    public $password;

    /**
     * @var int
     */
    public $privilege;

    /**
     * @var TemplateData[]
     */
    public $template;

    public function __construct(array $data = [])
    {
        $this->pin = $data['PIN'] ?? '';
        $this->name = $data['Name'] ?? '';
        $this->rfid = $data['RFID'] ?? '';
        $this->password = $data['Password'] ?? '';
        $this->privilege = (int)($data['Privilege'] ?? 0);
        $this->template = [];
        
        if (isset($data['Template']) && is_array($data['Template'])) {
            foreach ($data['Template'] as $item) {
                $this->template[] = new TemplateData($item);
            }
        }
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        $templateArray = [];
        foreach ($this->template as $item) {
            $templateArray[] = $item->toArray();
        }

        return [
            'PIN' => $this->pin,
            'Name' => $this->name,
            'RFID' => $this->rfid,
            'Password' => $this->password,
            'Privilege' => $this->privilege,
            'Template' => $templateArray
        ];
    }
}

class TemplateData
{
    /**
     * @var string
     */
    public $pin;

    /**
     * @var int
     */
    public $idx;

    /**
     * @var int
     */
    public $algVer;

    /**
     * @var string
     */
    public $template;

    public function __construct(array $data = [])
    {
        $this->pin = $data['pin'] ?? '';
        $this->idx = (int)($data['idx'] ?? 0);
        $this->algVer = (int)($data['alg_ver'] ?? 0);
        $this->template = $data['template'] ?? '';
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'pin' => $this->pin,
            'idx' => $this->idx,
            'alg_ver' => $this->algVer,
            'template' => $this->template
        ];
    }
}
