<?php

namespace Fingerplus\Services;

use Fingerplus\Fingerplus;
use Fingerplus\Models\User;
use Fingerplus\Models\Response;
use Exception;

class UserService
{
    /**
     * @var Fingerplus
     */
    private $fingerplus;

    public function __construct(Fingerplus $fingerplus)
    {
        $this->fingerplus = $fingerplus;
    }

    /**
     * Get all users
     */
    public function getAllUser(string $serialNumber): User
    {
        $params = ['sn' => $serialNumber];
        
        try {
            $response = $this->fingerplus->makeRequest(Fingerplus::GET_ALL_USER, $params);
            
            if ($response['status_code'] !== 200) {
                throw new Exception('HTTP Error: ' . $response['status_code']);
            }

            return User::fromArray($response['body']);
            
        } catch (Exception $e) {
            throw new Exception('Failed to get all users: ' . $e->getMessage());
        }
    }

    /**
     * Set user (TODO: Implementation needed)
     */
    public function setUser(string $serialNumber, array $userData): Response
    {
        // TODO: Implement user creation/update functionality
        throw new Exception('SetUser method not yet implemented');
    }

    /**
     * Delete all users (TODO: Implementation needed)
     */
    public function deleteAllUser(string $serialNumber): Response
    {
        // TODO: Implement delete all users functionality
        throw new Exception('DeleteAllUser method not yet implemented');
    }

    /**
     * Delete user by PIN (TODO: Implementation needed)
     */
    public function deleteUserPIN(string $serialNumber, string $pin): Response
    {
        // TODO: Implement delete user by PIN functionality
        throw new Exception('DeleteUserPIN method not yet implemented');
    }
}
