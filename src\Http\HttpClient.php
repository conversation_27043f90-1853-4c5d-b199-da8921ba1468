<?php

namespace Fingerplus\Http;

use Exception;

class HttpClient
{
    /**
     * @var int
     */
    private $timeout = 30;

    /**
     * @var resource|null
     */
    private $curl;

    public function __construct(int $timeout = 30)
    {
        $this->timeout = $timeout;
    }

    /**
     * Make a GET request
     */
    public function get(string $url, array $params = [], array $headers = []): array
    {
        if (!empty($params)) {
            $url .= '?' . http_build_query($params);
        }

        return $this->makeRequest('GET', $url, null, $headers);
    }

    /**
     * Make a POST request
     */
    public function post(string $url, array $params = [], array $headers = []): array
    {
        $body = http_build_query($params);
        return $this->makeRequest('POST', $url, $body, $headers);
    }

    /**
     * Make a POST request with JSON body
     */
    public function postJson(string $url, array $data = [], array $headers = []): array
    {
        $headers['Content-Type'] = 'application/json';
        $body = json_encode($data);
        return $this->makeRequest('POST', $url, $body, $headers);
    }

    /**
     * Make a POST request with raw body
     */
    public function postRaw(string $url, string $body, array $headers = []): array
    {
        return $this->makeRequest('POST', $url, $body, $headers);
    }

    /**
     * Make HTTP request using cURL
     */
    private function makeRequest(string $method, string $url, ?string $body = null, array $headers = []): array
    {
        $this->curl = curl_init();

        if ($this->curl === false) {
            throw new Exception('Failed to initialize cURL');
        }

        try {
            // Basic cURL options
            curl_setopt_array($this->curl, [
                CURLOPT_URL => $url,
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_TIMEOUT => $this->timeout,
                CURLOPT_FOLLOWLOCATION => true,
                CURLOPT_SSL_VERIFYPEER => false,
                CURLOPT_SSL_VERIFYHOST => false,
                CURLOPT_CUSTOMREQUEST => $method,
            ]);

            // Set headers
            if (!empty($headers)) {
                $headerArray = [];
                foreach ($headers as $key => $value) {
                    $headerArray[] = $key . ': ' . $value;
                }
                curl_setopt($this->curl, CURLOPT_HTTPHEADER, $headerArray);
            }

            // Set body for POST requests
            if ($method === 'POST' && $body !== null) {
                curl_setopt($this->curl, CURLOPT_POSTFIELDS, $body);
            }

            $response = curl_exec($this->curl);
            $httpCode = curl_getinfo($this->curl, CURLINFO_HTTP_CODE);
            $error = curl_error($this->curl);

            if ($response === false || !empty($error)) {
                throw new Exception('cURL error: ' . $error);
            }

            $decodedResponse = json_decode($response, true);
            
            if (json_last_error() !== JSON_ERROR_NONE) {
                throw new Exception('Invalid JSON response: ' . json_last_error_msg());
            }

            return [
                'status_code' => $httpCode,
                'body' => $decodedResponse,
                'raw_body' => $response
            ];

        } finally {
            if ($this->curl) {
                curl_close($this->curl);
                $this->curl = null;
            }
        }
    }

    /**
     * Set request timeout
     */
    public function setTimeout(int $timeout): void
    {
        $this->timeout = $timeout;
    }

    /**
     * Get current timeout
     */
    public function getTimeout(): int
    {
        return $this->timeout;
    }
}
