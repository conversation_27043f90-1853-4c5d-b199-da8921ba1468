# sebastian/recursion-context

[![CI Status](https://github.com/sebastianbergmann/recursion-context/workflows/CI/badge.svg)](https://github.com/sebastianbergmann/recursion-context/actions)
[![Type Coverage](https://shepherd.dev/github/sebastian<PERSON>mann/recursion-context/coverage.svg)](https://shepherd.dev/github/sebastian<PERSON>mann/recursion-context)

## Installation

You can add this library as a local, per-project dependency to your project using [Composer](https://getcomposer.org/):

```
composer require sebastian/recursion-context
```

If you only need this library during development, for instance to run your project's test suite, then you should add it as a development-time dependency:

```
composer require --dev sebastian/recursion-context
```
