<?php

namespace Fingerplus\Models;

class ScanLog
{
    /**
     * @var bool
     */
    public $result;

    /**
     * @var ScanLogData[]
     */
    public $data;

    public function __construct(array $data = [])
    {
        $this->result = $data['Result'] ?? false;
        $this->data = [];
        
        if (isset($data['Data']) && is_array($data['Data'])) {
            foreach ($data['Data'] as $item) {
                $this->data[] = new ScanLogData($item);
            }
        }
    }

    /**
     * Create from array data
     */
    public static function fromArray(array $data): self
    {
        return new self($data);
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        $dataArray = [];
        foreach ($this->data as $item) {
            $dataArray[] = $item->toArray();
        }

        return [
            'Result' => $this->result,
            'Data' => $dataArray
        ];
    }
}

class ScanLogData
{
    /**
     * @var string
     */
    public $sn;

    /**
     * @var string
     */
    public $scanDate;

    /**
     * @var string
     */
    public $pin;

    /**
     * @var int
     */
    public $verifyMode;

    /**
     * @var int
     */
    public $ioMode;

    /**
     * @var int
     */
    public $workCode;

    public function __construct(array $data = [])
    {
        $this->sn = $data['SN'] ?? '';
        $this->scanDate = $data['ScanDate'] ?? '';
        $this->pin = $data['PIN'] ?? '';
        $this->verifyMode = (int)($data['VerifyMode'] ?? 0);
        $this->ioMode = (int)($data['IOMode'] ?? 0);
        $this->workCode = (int)($data['WorkCode'] ?? 0);
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'SN' => $this->sn,
            'ScanDate' => $this->scanDate,
            'PIN' => $this->pin,
            'VerifyMode' => $this->verifyMode,
            'IOMode' => $this->ioMode,
            'WorkCode' => $this->workCode
        ];
    }
}
