{"name": "phpunit/php-invoker", "description": "Invoke callables with a timeout", "type": "library", "keywords": ["process"], "homepage": "https://github.com/sebastian<PERSON>mann/php-invoker/", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>", "role": "lead"}], "support": {"issues": "https://github.com/sebastian<PERSON>mann/php-invoker/issues"}, "prefer-stable": true, "config": {"platform": {"php": "7.3.0"}, "optimize-autoloader": true, "sort-packages": true}, "require": {"php": ">=7.3"}, "require-dev": {"ext-pcntl": "*", "phpunit/phpunit": "^9.3"}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture/"]}, "suggest": {"ext-pcntl": "*"}, "extra": {"branch-alias": {"dev-master": "3.1-dev"}}}