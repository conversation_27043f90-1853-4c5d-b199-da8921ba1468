{"name": "sebastian/environment", "description": "Provides functionality to handle HHVM/PHP environments", "keywords": ["environment", "hhvm", "xdebug"], "homepage": "http://www.github.com/sebastianbergmann/environment", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}], "config": {"platform": {"php": "7.3.0"}, "optimize-autoloader": true, "sort-packages": true}, "prefer-stable": true, "require": {"php": ">=7.3"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "suggest": {"ext-posix": "*"}, "autoload": {"classmap": ["src/"]}, "extra": {"branch-alias": {"dev-master": "5.1-dev"}}}