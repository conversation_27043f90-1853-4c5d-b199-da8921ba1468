<?php

namespace Fingerplus\Tests;

use PHPUnit\Framework\TestCase;
use Fingerplus\Fingerplus;
use Fingerplus\Models\DeviceInfo;
use Fingerplus\Models\ScanLog;
use Fingerplus\Models\User;
use Fingerplus\Models\Response;

class FingerplusTest extends TestCase
{
    private $fingerplus;
    private $testServerUrl = 'http://test.example.com';
    private $testSerialNumber = 'TEST123456789';

    protected function setUp(): void
    {
        $this->fingerplus = new Fingerplus($this->testServerUrl);
    }

    public function testConstructor()
    {
        $this->assertInstanceOf(Fingerplus::class, $this->fingerplus);
        $this->assertEquals($this->testServerUrl, $this->fingerplus->getServerUrl());
    }

    public function testSetServerUrl()
    {
        $newUrl = 'http://new.example.com';
        $this->fingerplus->setServerUrl($newUrl);
        $this->assertEquals($newUrl, $this->fingerplus->getServerUrl());
    }

    public function testDeviceInfoModel()
    {
        $testData = [
            'Result' => true,
            'DEVINFO' => [
                'Jam' => '12:00:00',
                'Admin' => '1',
                'User' => '100',
                'FP' => '50',
                'CARD' => '25',
                'PWD' => '10',
                'All Operasional' => '200',
                'All Presensi' => '150',
                'New Operasional' => '5',
                'New Presensi' => '3'
            ]
        ];

        $deviceInfo = DeviceInfo::fromArray($testData);
        
        $this->assertTrue($deviceInfo->result);
        $this->assertEquals('12:00:00', $deviceInfo->data->jam);
        $this->assertEquals('1', $deviceInfo->data->admin);
        $this->assertEquals('100', $deviceInfo->data->user);
    }

    public function testScanLogModel()
    {
        $testData = [
            'Result' => true,
            'Data' => [
                [
                    'SN' => 'TEST123',
                    'ScanDate' => '2023-01-01 12:00:00',
                    'PIN' => '001',
                    'VerifyMode' => 1,
                    'IOMode' => 1,
                    'WorkCode' => 0
                ]
            ]
        ];

        $scanLog = ScanLog::fromArray($testData);
        
        $this->assertTrue($scanLog->result);
        $this->assertCount(1, $scanLog->data);
        $this->assertEquals('TEST123', $scanLog->data[0]->sn);
        $this->assertEquals('001', $scanLog->data[0]->pin);
    }

    public function testUserModel()
    {
        $testData = [
            'Result' => true,
            'Data' => [
                [
                    'PIN' => '001',
                    'Name' => 'Test User',
                    'RFID' => '123456',
                    'Password' => 'pass123',
                    'Privilege' => 1,
                    'Template' => [
                        [
                            'pin' => '001',
                            'idx' => 0,
                            'alg_ver' => 1,
                            'template' => 'template_data'
                        ]
                    ]
                ]
            ]
        ];

        $user = User::fromArray($testData);
        
        $this->assertTrue($user->result);
        $this->assertCount(1, $user->data);
        $this->assertEquals('001', $user->data[0]->pin);
        $this->assertEquals('Test User', $user->data[0]->name);
        $this->assertCount(1, $user->data[0]->template);
    }

    public function testResponseModel()
    {
        $testData = ['Result' => true];
        $response = Response::fromArray($testData);
        
        $this->assertTrue($response->result);
    }
}
