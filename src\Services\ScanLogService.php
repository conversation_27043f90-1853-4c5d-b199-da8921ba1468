<?php

namespace Fingerplus\Services;

use Fingerplus\Fingerplus;
use Fingerplus\Models\ScanLog;
use Fingerplus\Models\Response;
use Exception;

class ScanLogService
{
    /**
     * @var Fingerplus
     */
    private $fingerplus;

    public function __construct(Fingerplus $fingerplus)
    {
        $this->fingerplus = $fingerplus;
    }

    /**
     * Get all scan logs
     */
    public function getAllScanLog(string $serialNumber): ScanLog
    {
        $params = ['sn' => $serialNumber];
        
        try {
            $response = $this->fingerplus->makeRequest(Fingerplus::GET_ALL_SCAN_LOG, $params);
            
            if ($response['status_code'] !== 200) {
                throw new Exception('HTTP Error: ' . $response['status_code']);
            }

            return ScanLog::fromArray($response['body']);
            
        } catch (Exception $e) {
            throw new Exception('Failed to get all scan logs: ' . $e->getMessage());
        }
    }

    /**
     * Get new scan logs
     */
    public function getNewScanLog(string $serialNumber): ScanLog
    {
        $params = ['sn' => $serialNumber];
        
        try {
            $response = $this->fingerplus->makeRequest(Fingerplus::GET_NEW_SCAN_LOG, $params);
            
            if ($response['status_code'] !== 200) {
                throw new Exception('HTTP Error: ' . $response['status_code']);
            }

            return ScanLog::fromArray($response['body']);
            
        } catch (Exception $e) {
            throw new Exception('Failed to get new scan logs: ' . $e->getMessage());
        }
    }

    /**
     * Delete scan logs
     */
    public function deleteScanLog(string $serialNumber): Response
    {
        $params = ['sn' => $serialNumber];
        
        try {
            $response = $this->fingerplus->makeRequest(Fingerplus::DELETE_SCAN_LOG, $params);
            
            if ($response['status_code'] !== 200) {
                throw new Exception('HTTP Error: ' . $response['status_code']);
            }

            return Response::fromArray($response['body']);
            
        } catch (Exception $e) {
            throw new Exception('Failed to delete scan logs: ' . $e->getMessage());
        }
    }
}
