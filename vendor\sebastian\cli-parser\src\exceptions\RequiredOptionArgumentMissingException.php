<?php declare(strict_types=1);
/*
 * This file is part of sebastian/cli-parser.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace <PERSON>\CliParser;

use function sprintf;
use RuntimeException;

final class RequiredOptionArgumentMissingException extends RuntimeException implements Exception
{
    public function __construct(string $option)
    {
        parent::__construct(
            sprintf(
                'Required argument for option "%s" is missing',
                $option
            )
        );
    }
}
