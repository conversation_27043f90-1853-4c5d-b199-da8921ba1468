<?php

namespace Fingerplus\Services;

use Fingerplus\Fingerplus;
use Fingerplus\Models\DeviceInfo;
use Fingerplus\Models\Response;
use Exception;

class DeviceService
{
    /**
     * @var Fingerplus
     */
    private $fingerplus;

    public function __construct(Fingerplus $fingerplus)
    {
        $this->fingerplus = $fingerplus;
    }

    /**
     * Get device information
     */
    public function getDeviceInfo(string $serialNumber): DeviceInfo
    {
        $params = ['sn' => $serialNumber];
        
        try {
            $response = $this->fingerplus->makeRequest(Fingerplus::GET_DEVICE_INFO, $params);
            
            if ($response['status_code'] !== 200) {
                throw new Exception('HTTP Error: ' . $response['status_code']);
            }

            return DeviceInfo::fromArray($response['body']);
            
        } catch (Exception $e) {
            throw new Exception('Failed to get device info: ' . $e->getMessage());
        }
    }

    /**
     * Set device time
     */
    public function setTime(string $serialNumber): Response
    {
        $params = ['sn' => $serialNumber];
        
        try {
            $response = $this->fingerplus->makeRequest(Fingerplus::SET_TIME, $params);
            
            if ($response['status_code'] !== 200) {
                throw new Exception('HTTP Error: ' . $response['status_code']);
            }

            return Response::fromArray($response['body']);
            
        } catch (Exception $e) {
            throw new Exception('Failed to set time: ' . $e->getMessage());
        }
    }
}
