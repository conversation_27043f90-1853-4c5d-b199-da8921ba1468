<?php

namespace Fingerplus;

use Fingerplus\Http\HttpClient;
use Fingerplus\Services\DeviceService;
use Fingerplus\Services\ScanLogService;
use Fingerplus\Services\UserService;

class Fingerplus
{
    // API endpoints
    const GET_DEVICE_INFO = '/dev/info';
    const SET_TIME = '/dev/settime';
    
    const GET_ALL_SCAN_LOG = '/scanlog/all';
    const GET_NEW_SCAN_LOG = '/scanlog/new';
    const DELETE_SCAN_LOG = '/scanlog/del';
    
    const GET_ALL_USER = '/user/all';
    const SET_USER = '/user/set';
    const DELETE_USER = '/user/delall';
    const DELETE_USER_PIN = '/user/del';

    /**
     * @var string
     */
    private $serverUrl;

    /**
     * @var HttpClient
     */
    private $httpClient;

    /**
     * @var DeviceService
     */
    private $deviceService;

    /**
     * @var ScanLogService
     */
    private $scanLogService;

    /**
     * @var UserService
     */
    private $userService;

    /**
     * @var array
     */
    private $defaultHeaders = [
        'Cache-Control' => 'no-cache',
        'Content-Type' => 'application/x-www-form-urlencoded'
    ];

    public function __construct(string $serverUrl = '')
    {
        $this->serverUrl = $serverUrl;
        $this->httpClient = new HttpClient();

        // Initialize services
        $this->deviceService = new DeviceService($this);
        $this->scanLogService = new ScanLogService($this);
        $this->userService = new UserService($this);
    }

    /**
     * Set the server URL
     */
    public function setServerUrl(string $serverUrl): void
    {
        $this->serverUrl = $serverUrl;
    }

    /**
     * Get the server URL
     */
    public function getServerUrl(): string
    {
        return $this->serverUrl;
    }

    /**
     * Get the HTTP client instance
     */
    public function getHttpClient(): HttpClient
    {
        return $this->httpClient;
    }

    /**
     * Get default headers
     */
    public function getDefaultHeaders(): array
    {
        return $this->defaultHeaders;
    }

    /**
     * Make a POST request to the API
     */
    public function makeRequest(string $endpoint, array $params = []): array
    {
        $url = $this->serverUrl . $endpoint;

        return $this->httpClient->post($url, $params, $this->defaultHeaders);
    }

    // Device methods
    public function getDeviceInfo(string $serialNumber)
    {
        return $this->deviceService->getDeviceInfo($serialNumber);
    }

    public function setTime(string $serialNumber)
    {
        return $this->deviceService->setTime($serialNumber);
    }

    // ScanLog methods
    public function getAllScanLog(string $serialNumber)
    {
        return $this->scanLogService->getAllScanLog($serialNumber);
    }

    public function getNewScanLog(string $serialNumber)
    {
        return $this->scanLogService->getNewScanLog($serialNumber);
    }

    public function deleteScanLog(string $serialNumber)
    {
        return $this->scanLogService->deleteScanLog($serialNumber);
    }

    // User methods
    public function getAllUser(string $serialNumber)
    {
        return $this->userService->getAllUser($serialNumber);
    }

    public function setUser(string $serialNumber, array $userData)
    {
        return $this->userService->setUser($serialNumber, $userData);
    }

    public function deleteAllUser(string $serialNumber)
    {
        return $this->userService->deleteAllUser($serialNumber);
    }

    public function deleteUserPIN(string $serialNumber, string $pin)
    {
        return $this->userService->deleteUserPIN($serialNumber, $pin);
    }
}
