{"name": "sebastian/comparator", "description": "Provides the functionality to compare PHP values for equality", "keywords": ["comparator", "compare", "equality"], "homepage": "https://github.com/sebas<PERSON><PERSON><PERSON>/comparator", "license": "BSD-3-<PERSON><PERSON>", "authors": [{"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON><PERSON>", "email": "<EMAIL>"}, {"name": "<PERSON>", "email": "<EMAIL>"}], "prefer-stable": true, "require": {"php": ">=7.3", "sebastian/diff": "^4.0", "sebastian/exporter": "^4.0"}, "require-dev": {"phpunit/phpunit": "^9.3"}, "config": {"platform": {"php": "7.3.0"}, "optimize-autoloader": true, "sort-packages": true}, "autoload": {"classmap": ["src/"]}, "autoload-dev": {"classmap": ["tests/_fixture"]}, "extra": {"branch-alias": {"dev-master": "4.0-dev"}}}