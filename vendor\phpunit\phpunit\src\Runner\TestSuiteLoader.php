<?php declare(strict_types=1);
/*
 * This file is part of PHPUnit.
 *
 * (c) <PERSON> <<EMAIL>>
 *
 * For the full copyright and license information, please view the LICENSE
 * file that was distributed with this source code.
 */
namespace PHPUnit\Runner;

use ReflectionClass;

/**
 * @deprecated see https://github.com/sebas<PERSON><PERSON>mann/phpunit/issues/4039
 *
 * @no-named-arguments Parameter names are not covered by the backward compatibility promise for PHPUnit
 */
interface TestSuiteLoader
{
    public function load(string $suiteClassFile): ReflectionClass;

    public function reload(ReflectionClass $aClass): ReflectionClass;
}
