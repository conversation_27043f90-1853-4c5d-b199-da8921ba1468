# Fingerplus PHP Library

This is a PHP conversion of the original Go fingerprint device management library. It provides a simple interface to interact with fingerprint devices through HTTP API calls.

## Features

- Device information retrieval
- Time synchronization
- Scan log management (get all, get new, delete)
- User management (get all users)
- HTTP client with cURL support
- Comprehensive data models
- PSR-4 autoloading

## Installation

1. Install dependencies using Composer:
```bash
composer install
```

## Usage

### Basic Example

```php
<?php
require_once 'vendor/autoload.php';

use Fingerplus\Fingerplus;

// Initialize the library
$fp = new Fingerplus('http://192.168.1.2:8080');

// Get device information
$deviceInfo = $fp->getDeviceInfo('2251016030256');
echo "Device Info: " . json_encode($deviceInfo->toArray(), JSON_PRETTY_PRINT);

// Get all scan logs
$scanLogs = $fp->getAllScanLog('2251016030256');
echo "Scan Logs: " . json_encode($scanLogs->toArray(), J<PERSON><PERSON>_PRETTY_PRINT);

// Get all users
$users = $fp->getAllUser('2251016030256');
echo "Users: " . json_encode($users->toArray(), JSON_PRETTY_PRINT);
```

### Available Methods

#### Device Management
- `getDeviceInfo(string $serialNumber)` - Get device information
- `setTime(string $serialNumber)` - Synchronize device time

#### Scan Log Management
- `getAllScanLog(string $serialNumber)` - Get all scan logs
- `getNewScanLog(string $serialNumber)` - Get new scan logs
- `deleteScanLog(string $serialNumber)` - Delete scan logs

#### User Management
- `getAllUser(string $serialNumber)` - Get all users
- `setUser(string $serialNumber, array $userData)` - Set user (TODO)
- `deleteAllUser(string $serialNumber)` - Delete all users (TODO)
- `deleteUserPIN(string $serialNumber, string $pin)` - Delete user by PIN (TODO)

## Project Structure

```
src/
├── Fingerplus.php          # Main class
├── Http/
│   └── HttpClient.php      # HTTP client wrapper
├── Models/                 # Data models
│   ├── DeviceInfo.php
│   ├── Response.php
│   ├── ScanLog.php
│   └── User.php
└── Services/               # Service classes
    ├── DeviceService.php
    ├── ScanLogService.php
    └── UserService.php
```

## Testing

Run the test suite:
```bash
./vendor/bin/phpunit
```

## Conversion Notes

This PHP library maintains the same API structure and functionality as the original Go version:

- **Go structs** → **PHP classes with properties**
- **Go methods** → **PHP methods**
- **Go error handling** → **PHP exceptions**
- **Go JSON marshaling** → **PHP JSON encoding/decoding**
- **Go HTTP client** → **PHP cURL wrapper**

### Key Differences

1. **Error Handling**: Go's explicit error returns are replaced with PHP exceptions
2. **Type System**: Go's strict typing is replaced with PHP's dynamic typing with type hints
3. **Memory Management**: PHP's garbage collection vs Go's manual memory management
4. **Concurrency**: Go's goroutines are not directly applicable in PHP (single-threaded)

## Requirements

- PHP 7.4 or higher
- cURL extension
- JSON extension

## License

This project maintains the same license as the original Go version.
