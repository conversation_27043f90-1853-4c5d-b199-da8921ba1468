<?php

namespace Fingerplus\Models;

class Response
{
    /**
     * @var bool
     */
    public $result;

    public function __construct(array $data = [])
    {
        $this->result = $data['Result'] ?? false;
    }

    /**
     * Create from array data
     */
    public static function fromArray(array $data): self
    {
        return new self($data);
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'Result' => $this->result
        ];
    }
}
