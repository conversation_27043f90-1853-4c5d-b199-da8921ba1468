<?php

require_once 'vendor/autoload.php';

use Fingerplus\Fingerplus;

// Configuration
const SERVER_URL = 'http://192.168.1.2:8080';
const SERIAL_NUMBER = '2251016030256';

function main()
{
    // Initialize Fingerplus
    $fp = new Fingerplus(SERVER_URL);

    try {
        // Get device information
        echo "Getting device info...\n";
        $devInfo = $fp->getDeviceInfo(SERIAL_NUMBER);
        echo "GetDeviceInfo: " . json_encode($devInfo->toArray(), JSON_PRETTY_PRINT) . "\n\n";

        // Set time
        echo "Setting time...\n";
        $setTime = $fp->setTime(SERIAL_NUMBER);
        echo "SetTime: " . json_encode($setTime->toArray(), JSON_PRETTY_PRINT) . "\n\n";

        // Get all scan logs
        echo "Getting all scan logs...\n";
        $allScanLog = $fp->getAllScanLog(SERIAL_NUMBER);
        echo "GetAllScanLog: " . json_encode($allScanLog->toArray(), JSON_PRETTY_PRINT) . "\n\n";

        // Get new scan logs
        echo "Getting new scan logs...\n";
        $newScanLog = $fp->getNewScanLog(SERIAL_NUMBER);
        echo "GetNewScanLog: " . json_encode($newScanLog->toArray(), JSON_PRETTY_PRINT) . "\n\n";

        // Delete scan logs
        echo "Deleting scan logs...\n";
        $delScanLog = $fp->deleteScanLog(SERIAL_NUMBER);
        echo "DeleteScanLog: " . json_encode($delScanLog->toArray(), JSON_PRETTY_PRINT) . "\n\n";

        // Get all users
        echo "Getting all users...\n";
        $allUser = $fp->getAllUser(SERIAL_NUMBER);
        echo "GetAllUser: " . json_encode($allUser->toArray(), JSON_PRETTY_PRINT) . "\n\n";

    } catch (Exception $e) {
        echo "Error: " . $e->getMessage() . "\n";
        echo "Stack trace:\n" . $e->getTraceAsString() . "\n";
    }
}

// Run the example
main();
