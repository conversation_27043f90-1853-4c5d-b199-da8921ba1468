<?php

namespace Fingerplus\Models;

class DeviceInfo
{
    /**
     * @var bool
     */
    public $result;

    /**
     * @var DeviceInfoData
     */
    public $data;

    public function __construct(array $data = [])
    {
        $this->result = $data['Result'] ?? false;
        $this->data = new DeviceInfoData($data['DEVINFO'] ?? []);
    }

    /**
     * Create from array data
     */
    public static function fromArray(array $data): self
    {
        return new self($data);
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'Result' => $this->result,
            'DEVINFO' => $this->data->toArray()
        ];
    }
}

class DeviceInfoData
{
    /**
     * @var string
     */
    public $jam;

    /**
     * @var string
     */
    public $admin;

    /**
     * @var string
     */
    public $user;

    /**
     * @var string
     */
    public $fp;

    /**
     * @var string
     */
    public $card;

    /**
     * @var string
     */
    public $pwd;

    /**
     * @var string
     */
    public $allOperasional;

    /**
     * @var string
     */
    public $allPresensi;

    /**
     * @var string
     */
    public $newOperasional;

    /**
     * @var string
     */
    public $newPresensi;

    public function __construct(array $data = [])
    {
        $this->jam = $data['Jam'] ?? '';
        $this->admin = $data['Admin'] ?? '';
        $this->user = $data['User'] ?? '';
        $this->fp = $data['FP'] ?? '';
        $this->card = $data['CARD'] ?? '';
        $this->pwd = $data['PWD'] ?? '';
        $this->allOperasional = $data['All Operasional'] ?? '';
        $this->allPresensi = $data['All Presensi'] ?? '';
        $this->newOperasional = $data['New Operasional'] ?? '';
        $this->newPresensi = $data['New Presensi'] ?? '';
    }

    /**
     * Convert to array
     */
    public function toArray(): array
    {
        return [
            'Jam' => $this->jam,
            'Admin' => $this->admin,
            'User' => $this->user,
            'FP' => $this->fp,
            'CARD' => $this->card,
            'PWD' => $this->pwd,
            'All Operasional' => $this->allOperasional,
            'All Presensi' => $this->allPresensi,
            'New Operasional' => $this->newOperasional,
            'New Presensi' => $this->newPresensi
        ];
    }
}
